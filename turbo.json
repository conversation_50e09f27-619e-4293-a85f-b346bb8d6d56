{"$schema": "https://turborepo.com/schema.json", "ui": "tui", "tasks": {"build": {"dependsOn": ["^build", "^db:generate"], "inputs": ["$TURBO_DEFAULT$", ".env*"], "outputs": ["dist/**", ".next/**", "!.next/cache/**"]}, "lint": {"dependsOn": ["^lint"]}, "check-types": {"dependsOn": ["^check-types"]}, "start": {"dependsOn": ["^build"], "cache": false}, "dev": {"dependsOn": ["^build", "^db:generate"], "cache": false, "persistent": true}, "db:generate": {"cache": false}, "db:migrate": {"cache": false, "persistent": true}, "db:deploy": {"cache": false}}}