import { Request, Response } from "express";
import EmailPassword from "supertokens-node/recipe/emailpassword";
import UserMetadata from "supertokens-node/recipe/usermetadata";
import { listUsersByAccountInfo } from "supertokens-node";
import { EmailController } from "./emailController";
const TENANT_ID = process.env.SUPERTOKENS_TENANT_ID || "public";

export class UserController {
  /**
   * Get current authenticated user details
   */
  static async getCurrentUser(req: Request, res: Response) {
    try {
      const session = req.session!;
      const userId = session.getUserId();

      // Get user details
      // const userInfo = await EmailPassword.getUserById(userId);

      // Get user metadata
      const metadata = await UserMetadata.getUserMetadata(userId);

      return res.json({
        status: "OK",
        user: metadata.metadata,
      });
    } catch (err) {
      console.error(err);
      return res.status(500).json({ error: "Something went wrong" });
    }
  }

  /**
   * Get user by ID (for authenticated user)
   */
  static async getUserById(req: Request, res: Response) {
    try {
      const userId = req.session!.getUserId();
      const user = await EmailPassword.getUserById(userId);
      return res.json({ user });
    } catch (err) {
      console.error("get user error:", err);
      return res.status(500).json({ error: "Failed to fetch user" });
    }
  }

  static async createUserDirectly(email: string, password: string, metadata: any = {}) {
    const response = await EmailPassword.signUp("public", email, password);
    if (response.status === "OK") {
      await UserMetadata.updateUserMetadata(response.user.id, metadata);
    } else {
      return null;
    }
    await EmailController.sendEmailVerificationToUserByEmail({email,...metadata});
    return null;
  }

  /**
   * Create a new user (admin function)
   */
  static async createUser(req: Request, res: Response) {
    try {
      const { email, password } = req.body;

      const response = await EmailPassword.signUp("public", email, password);

      if (response.status === "OK") {
        return res.json({ status: "OK", user: response.user });
      } else if (response.status === "EMAIL_ALREADY_EXISTS_ERROR") {
        return res.status(400).json({ status: "EMAIL_ALREADY_EXISTS_ERROR" });
      }
    } catch (err) {
      console.error(err);
      return res.status(500).json({ error: "Something went wrong" });
    }
  }

  /**
   * Check if email already exists in the system
   */
  static async checkEmailExists(req: Request, res: Response) {
    try {
      const email = String(req.query.email || "");
      if (!email) {
        return res.status(400).json({ error: "email query param is required" });
      }

      const users = await listUsersByAccountInfo(TENANT_ID, { email });
      return res.json({ exists: Array.isArray(users) && users.length > 0 });
    } catch (err) {
      console.error("email exists check error:", err);
      return res.status(500).json({ error: "Internal error" });
    }
  }

  /**
   * Update user metadata
   */
  static async updateUserMetadata(req: Request, res: Response) {
    try {
      const userId = req.session!.getUserId();
      const { metadata } = req.body;

      if (!metadata) {
        return res.status(400).json({ error: "metadata is required" });
      }

      await UserMetadata.updateUserMetadata(userId, metadata);
      const updatedMetadata = await UserMetadata.getUserMetadata(userId);

      return res.json({
        status: "OK",
        metadata: updatedMetadata.metadata,
      });
    } catch (err) {
      console.error("update metadata error:", err);
      return res.status(500).json({ error: "Failed to update metadata" });
    }
  }

  /**
   * Delete user metadata
   */
  static async clearUserMetadata(req: Request, res: Response) {
    try {
      const userId = req.session!.getUserId();

      await UserMetadata.clearUserMetadata(userId);

      return res.json({ status: "OK" });
    } catch (err) {
      console.error("clear metadata error:", err);
      return res.status(500).json({ error: "Failed to clear metadata" });
    }
  }
}
