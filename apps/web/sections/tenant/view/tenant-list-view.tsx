"use client";
import React, { useCallback, useState } from "react";
import { SearchBar } from "@/components/search-bar";
import TenantTable from "../tenant-table";
import { SubNavBar } from "@/components/sub-nav-bar";
import { Dialog, DialogContent, DialogHeader, DialogTitle } from "@repo/ui/components/dialog";
import AddTenantForm from "../add-tenant-form";
import { useGetTenantList } from "@/api-slice/tenant";
import Loading from "@repo/ui/components/loading";
import { ErrorBoundary } from "@/components/error-boundary";

const TenantListView: React.FC = () => {
  const [open, setOpen] = useState(false);
  const { data, fetchNextPage, hasNextPage, isFetchingNextPage, isLoading, error, refetch } =
    useGetTenantList(5);

  // Flatten pages into one list
  const tenants = data?.pages.flatMap((page) => page.tenants) ?? [];

  console.log(tenants, "tenants data");

  // Scroll handler (when near bottom, fetch next page)
  const handleScrollEnd = useCallback(() => {
    if (hasNextPage && !isFetchingNextPage) {
      fetchNextPage();
    }
  }, [hasNextPage, isFetchingNextPage, fetchNextPage]);

  if (isLoading) return <Loading />;

  if (error) return <ErrorBoundary error={error} onRetry={() => refetch()} />;

  return (
    <div className="flex h-full flex-col">
      <SearchBar />

      <div className="py-2">
        <SubNavBar actionButton={[{ name: "Add Tenant", onClick: () => setOpen(true) }]} />
      </div>

      <TenantTable
        tenantData={tenants ?? []}
        onEndReached={handleScrollEnd}
        hasNextPage={hasNextPage}
        isFetchingNextPage={isFetchingNextPage}
      />

      <Dialog open={open} onOpenChange={setOpen}>
        <DialogContent className="sm:max-w-[700px]">
          <DialogHeader>
            <DialogTitle className="text-center">Tenant Registry</DialogTitle>
          </DialogHeader>
          <AddTenantForm onSuccess={() => setOpen(false)} />
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default TenantListView;
