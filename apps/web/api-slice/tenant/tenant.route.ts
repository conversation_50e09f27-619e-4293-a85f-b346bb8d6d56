import { useQuery, useMutation, useQueryClient, useInfiniteQuery } from "@tanstack/react-query";
import instance from "../lib/instance";

export type CreateTenantPayload = {
  companyName: string;
  adminName: string;
  emailId: string;
  subscriptionType: string;
  contactNo: string;
  capOnUsers: number;
  address: string;
};

type EditTenantPayload = {
  tenantId: string;
  data: CreateTenantPayload;
};

type ActivateDeactivate = {
  tenantId: string;
  data: {
    isActive: boolean;
  };
};

export type Tenant = {
  id: string;
  tenantId: string;
  companyName: string;
  adminName: string;
  emailId: string;
  subscriptionType: string;
  contactNo: string;
  capOnUsers: number;
  address: string;
  createdAt: string;
  updatedAt: string;
  first_time_user: boolean;
  isActive: boolean;
  users: Array<{
    id: string;
    firstName: string;
    lastName: string;
    displayName: string;
    emailId: string;
    role: string;
    isActive: boolean;
    tenantId: string;
    createdAt: string;
    updatedAt: string;
    lastLoginAt: string | null;
    first_time_user: boolean;
  }>;
};

type GetTenantResponse = {
  tenants: Tenant[];
  nextCursor?: string;
};

// GET request with useQuery
export function useTenantData(tenantId: string) {
  return useQuery({
    queryKey: ["tenants", tenantId],
    queryFn: async () => {
      const response = await instance.get(`/tenants/${tenantId}`);
      return response.data;
    },
  });
}

export function useGetTenants() {
  return useQuery({
    queryKey: ["tenants"],
    queryFn: async (): Promise<Tenant[]> => {
      const response = await instance.get(`/tenants`);
      console.log("useGetTenants query", response);
      return response.data;
    },
  });
}

export function useGetTenantList(limit = 10) {
  return useInfiniteQuery({
    queryKey: ["tenants"],
    initialPageParam: undefined,
    queryFn: async ({ pageParam }: { pageParam?: string }) => {
      const response = await instance.post<GetTenantResponse>(`/tenants/list`, {
        limit,
        cursor: pageParam,
      });
      return response.data;
    },
    getNextPageParam: (lastPage) => lastPage.nextCursor ?? undefined,
  });
}

// POST request with useMutation
export function useCreateTenant() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (userData: CreateTenantPayload) => {
      const response = await instance.post("/tenants", userData);
      return response.data;
    },
    onSuccess: () => {
      // Invalidate and refetch tenants queries
      queryClient.invalidateQueries({ queryKey: ["tenants"] });
    },
  });
}

export function useEditTenant() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({ tenantId, data }: EditTenantPayload) => {
      const response = await instance.put(`/tenants/${tenantId}`, data);
      return response.data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["tenants"] });
    },
  });
}

// ACTIVATE user
export function useActivateTenant() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({ tenantId, data }: ActivateDeactivate) => {
      const response = await instance.patch(`/tenants/${tenantId}/activate`, data);
      return response.data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["tenants"] });
    },
  });
}

// DEACTIVATE user
export function useDeactivateTenant() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({ tenantId, data }: ActivateDeactivate) => {
      const response = await instance.patch(`/tenants/${tenantId}/deactivate`, data);
      return response.data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["tenants"] });
    },
  });
}

// DELETE user
export function useDeleteTenant() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (tenantId: string) => {
      const response = await instance.delete(`/tenants/${tenantId}`);
      return response.data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["tenants"] });
    },
  });
}
