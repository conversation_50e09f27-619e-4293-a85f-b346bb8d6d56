import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@repo/ui/components/select";
import React from "react";
import { useGetTenants } from "@/api-slice/tenant";

const TenantSelector: React.FC = () => {
  const { data: tenants, isLoading, error } = useGetTenants();

  if (isLoading) return <div>Loading... Tenants</div>;
  if (error) return <div>Something went wrong</div>;

  return (
    <Select>
      <SelectTrigger className="h-10 w-[200px]">
        <SelectValue placeholder="Select an tenant" />
      </SelectTrigger>
      <SelectContent>
        {tenants?.map((item) => (
          <SelectItem key={item.id} value={item.id}>
            {item.companyName}
          </SelectItem>
        ))}
      </SelectContent>
    </Select>
  );
};
export default TenantSelector;
